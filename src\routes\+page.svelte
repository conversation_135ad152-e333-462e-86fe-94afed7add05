<script>
	import Footer from './Footer.svelte';
	import {
		OWNER_NAME,
		COMPANY_NAME,
		COMPANY_DESCRIPTION,
		PHONE_NUMBER,
		EMAIL,
		SEO_TITLE,
		SEO_DESCRIPTION
	} from '$lib/constants.js';
</script>

<svelte:head>
	<title>{OWNER_NAME} | {SEO_TITLE}</title>
	<meta name="description" content={SEO_DESCRIPTION} />
</svelte:head>

<!-- Brand Header -->
<section>
	<div class="w-full bg-white shadow-lg">
		<div class="px-4 sm:px-6 md:px-8">
			<!-- Header content -->
			<div class="flex items-start gap-3 py-4">
				<div class="h-14 w-14 flex-shrink-0 overflow-hidden rounded-xl sm:h-12 sm:w-12">
					<img
						src="../logo-icon.png"
						alt="Logo of {COMPANY_NAME}"
						class="h-full w-full object-cover"
					/>
				</div>
				<!-- Company info -->
				<div>
					<a href="/" class="block text-left text-xl font-bold text-gray-800 hover:no-underline">
						{COMPANY_NAME}
					</a>
					<h2 class="text-left text-xs text-neutral/70">{COMPANY_DESCRIPTION}</h2>
				</div>
			</div>
		</div>
	</div>
</section>

<!-- Hero Section -->
<section
	class="relative overflow-hidden bg-gradient-to-br from-blue-900 via-blue-800 to-blue-700 py-10 text-white"
>
	<!-- Heroicos background element -->
	<div class="absolute inset-0 flex items-center justify-center space-x-32 opacity-5">
		<!-- Lightning bolt -->
		<svg
			class="w-126 h-126 text-blue-200"
			fill="none"
			viewBox="0 0 24 24"
			stroke-width="1.5"
			stroke="currentColor"
		>
			<path
				stroke-linecap="round"
				stroke-linejoin="round"
				d="m3.75 13.5 10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75Z"
			/>
		</svg>
	</div>

	<div class="container relative mx-auto px-4">
		<div class="mx-auto max-w-4xl text-center">
			<h3 class="md:text-4l text-shadow mb-6 text-5xl font-bold leading-tight">
				Your electrician
				<span class="block text-yellow-300">in the Scottish Borders</span>
			</h3>

			<h4 class="mx-auto mb-8 max-w-3xl text-xl leading-relaxed text-blue-100 md:text-2xl">
				Serving homes, farms, and businesses across the Scottish Borders with over 30 years of
				hands-on electrical experience from a fully qualified, time-served electrician.
			</h4>

			<div class="mb-10 flex flex-col justify-center gap-4 sm:flex-row">
				<a href="tel:{PHONE_NUMBER}" class="btn-base-100 btn" aria-label="Call {PHONE_NUMBER}">
					<svg
						xmlns="http://www.w3.org/2000/svg"
						viewBox="0 0 20 20"
						fill="currentColor"
						class="size-5"
						aria-hidden="true"
					>
						<path
							fill-rule="evenodd"
							d="M2 3.5A1.5 1.5 0 0 1 3.5 2h1.148a1.5 1.5 0 0 1 1.465 1.175l.716 3.223a1.5 1.5 0 0 1-1.052 1.767l-.933.267c-.41.117-.643.555-.48.95a11.542 11.542 0 0 0 6.254 6.254c.395.163.833-.07.95-.48l.267-.933a1.5 1.5 0 0 1 1.767-1.052l3.223.716A1.5 1.5 0 0 1 18 15.352V16.5a1.5 1.5 0 0 1-1.5 1.5H15c-1.149 0-2.263-.15-3.326-.43A13.022 13.022 0 0 1 2.43 8.326 13.019 13.019 0 0 1 2 5V3.5Z"
							clip-rule="evenodd"
						/>
					</svg>
					Call Now: {PHONE_NUMBER}
				</a>

				<a
					href="mailto:{EMAIL}?subject=Website Enquiry"
					class="btn btn-warning"
					aria-label="Send email to {EMAIL}"
				>
					<svg
						xmlns="http://www.w3.org/2000/svg"
						fill="none"
						viewBox="0 0 24 24"
						stroke-width="1.5"
						stroke="currentColor"
						class="size-6"
						aria-hidden="true"
					>
						<path
							stroke-linecap="round"
							stroke-linejoin="round"
							d="M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75"
						/>
					</svg>
					Email Us
				</a>
			</div>

			<div class="flex flex-wrap justify-center gap-6 text-sm text-blue-200">
				<div class="flex items-center gap-1">
					<svg
						xmlns="http://www.w3.org/2000/svg"
						viewBox="0 0 20 20"
						fill="currentColor"
						class="size-5"
						aria-hidden="true"
					>
						<path
							fill-rule="evenodd"
							d="M16.403 12.652a3 3 0 0 0 0-5.304 3 3 0 0 0-3.75-3.751 3 3 0 0 0-5.305 0 3 3 0 0 0-3.751 3.75 3 3 0 0 0 0 5.305 3 3 0 0 0 3.75 3.751 3 3 0 0 0 5.305 0 3 3 0 0 0 3.751-3.75Zm-2.546-4.46a.75.75 0 0 0-1.214-.883l-3.483 4.79-1.88-1.88a.75.75 0 1 0-1.06 1.061l2.5 2.5a.75.75 0 0 0 1.137-.089l4-5.5Z"
							clip-rule="evenodd"
						/>
					</svg>
					Professionally Insured
				</div>
				<div class="flex items-center gap-1">
					<svg
						xmlns="http://www.w3.org/2000/svg"
						viewBox="0 0 20 20"
						fill="currentColor"
						class="size-5"
						aria-hidden="true"
					>
						<path
							fill-rule="evenodd"
							d="M16.403 12.652a3 3 0 0 0 0-5.304 3 3 0 0 0-3.75-3.751 3 3 0 0 0-5.305 0 3 3 0 0 0-3.751 3.75 3 3 0 0 0 0 5.305 3 3 0 0 0 3.75 3.751 3 3 0 0 0 5.305 0 3 3 0 0 0 3.751-3.75Zm-2.546-4.46a.75.75 0 0 0-1.214-.883l-3.483 4.79-1.88-1.88a.75.75 0 1 0-1.06 1.061l2.5 2.5a.75.75 0 0 0 1.137-.089l4-5.5Z"
							clip-rule="evenodd"
						/>
					</svg>
					Fully Qualified
				</div>
				<div class="flex items-center gap-1">
					<svg
						xmlns="http://www.w3.org/2000/svg"
						viewBox="0 0 20 20"
						fill="currentColor"
						class="size-5"
						aria-hidden="true"
					>
						<path
							fill-rule="evenodd"
							d="M16.403 12.652a3 3 0 0 0 0-5.304 3 3 0 0 0-3.75-3.751 3 3 0 0 0-5.305 0 3 3 0 0 0-3.751 3.75 3 3 0 0 0 0 5.305 3 3 0 0 0 3.75 3.751 3 3 0 0 0 5.305 0 3 3 0 0 0 3.751-3.75Zm-2.546-4.46a.75.75 0 0 0-1.214-.883l-3.483 4.79-1.88-1.88a.75.75 0 1 0-1.06 1.061l2.5 2.5a.75.75 0 0 0 1.137-.089l4-5.5Z"
							clip-rule="evenodd"
						/>
					</svg>
					18th Edition Compliant
				</div>
			</div>
		</div>
	</div>
</section>

<!-- Main Content Section -->
<section class="bg-gradient-to-br from-gray-50 to-white py-10">
	<div class="container mx-auto px-4">
		<div class="mx-auto max-w-5xl">
			<div class="grid gap-8 sm:grid-cols-2 lg:grid-cols-4">
				<!-- Domestic Services -->
				<div
					class="card flex h-full flex-col rounded-2xl border border-gray-100 bg-white p-8 shadow-xl"
				>
					<div
						class="mx-auto mb-6 flex h-16 w-16 items-center justify-center rounded-xl bg-blue-100"
					>
						<img src="/blue_house.png" alt="House icon" class="h-8 w-8 object-contain" />
					</div>
					<h3 class="mb-5 text-center text-xl font-semibold text-gray-800">Domestic Services</h3>
					<ul class="mb-6 space-y-3 text-sm text-gray-600">
						<li class="flex">
							<svg
								class="mr-3 mt-1 h-5 w-5 flex-shrink-0 text-green-500"
								xmlns="http://www.w3.org/2000/svg"
								fill="none"
								viewBox="0 0 24 24"
								stroke-width="2"
								stroke="currentColor"
								aria-hidden="true"
							>
								<path stroke-linecap="round" stroke-linejoin="round" d="m4.5 12.75 6 6 9-13.5" />
							</svg>
							<span class="flex-1">Fault finding &amp; repairs for homeowners and landlords</span>
						</li>
						<li class="flex">
							<svg
								class="mr-3 mt-1 h-5 w-5 flex-shrink-0 text-green-500"
								xmlns="http://www.w3.org/2000/svg"
								fill="none"
								viewBox="0 0 24 24"
								stroke-width="2"
								stroke="currentColor"
								aria-hidden="true"
							>
								<path stroke-linecap="round" stroke-linejoin="round" d="m4.5 12.75 6 6 9-13.5" />
							</svg>
							<span class="flex-1">Fuse board (consumer unit) upgrades</span>
						</li>
						<li class="flex">
							<svg
								class="mr-3 mt-1 h-5 w-5 flex-shrink-0 text-green-500"
								xmlns="http://www.w3.org/2000/svg"
								fill="none"
								viewBox="0 0 24 24"
								stroke-width="2"
								stroke="currentColor"
								aria-hidden="true"
							>
								<path stroke-linecap="round" stroke-linejoin="round" d="m4.5 12.75 6 6 9-13.5" />
							</svg>
							<span class="flex-1">Safety inspections &amp; testing</span>
						</li>
						<li class="flex">
							<svg
								class="mr-3 mt-1 h-5 w-5 flex-shrink-0 text-green-500"
								xmlns="http://www.w3.org/2000/svg"
								fill="none"
								viewBox="0 0 24 24"
								stroke-width="2"
								stroke="currentColor"
								aria-hidden="true"
							>
								<path stroke-linecap="round" stroke-linejoin="round" d="m4.5 12.75 6 6 9-13.5" />
							</svg>
							<span class="flex-1">Modern indoor &amp; outdoor lighting solutions</span>
						</li>
					</ul>
					<div class="mt-auto text-center">
						<a href="/domestic" class="font-medium text-blue-600 no-underline hover:text-blue-700"
							>Learn More →</a
						>
					</div>
				</div>

				<!-- Agricultural Solutions -->
				<div
					class="card flex h-full flex-col rounded-2xl border border-gray-100 bg-white p-8 shadow-xl"
				>
					<div
						class="mx-auto mb-6 flex h-16 w-16 items-center justify-center rounded-xl bg-green-100"
					>
						<img src="/green-fields.png" alt="Green fields" class="h-8 w-8 object-contain" />
					</div>
					<h3 class="mb-5 text-center text-xl font-semibold text-gray-800">
						Agricultural Electrics
					</h3>
					<ul class="mb-6 space-y-3 text-sm text-gray-600">
						<li class="flex">
							<svg
								class="mr-3 mt-1 h-5 w-5 flex-shrink-0 text-green-500"
								xmlns="http://www.w3.org/2000/svg"
								fill="none"
								viewBox="0 0 24 24"
								stroke-width="2"
								stroke="currentColor"
								aria-hidden="true"
							>
								<path stroke-linecap="round" stroke-linejoin="round" d="m4.5 12.75 6 6 9-13.5" />
							</svg>
							<span class="flex-1">Barn & workshop wiring</span>
						</li>
						<li class="flex">
							<svg
								class="mr-3 mt-1 h-5 w-5 flex-shrink-0 text-green-500"
								xmlns="http://www.w3.org/2000/svg"
								fill="none"
								viewBox="0 0 24 24"
								stroke-width="2"
								stroke="currentColor"
								aria-hidden="true"
							>
								<path stroke-linecap="round" stroke-linejoin="round" d="m4.5 12.75 6 6 9-13.5" />
							</svg>
							<span class="flex-1">Farm equipment installation</span>
						</li>
						<li class="flex">
							<svg
								class="mr-3 mt-1 h-5 w-5 flex-shrink-0 text-green-500"
								xmlns="http://www.w3.org/2000/svg"
								fill="none"
								viewBox="0 0 24 24"
								stroke-width="2"
								stroke="currentColor"
								aria-hidden="true"
							>
								<path stroke-linecap="round" stroke-linejoin="round" d="m4.5 12.75 6 6 9-13.5" />
							</svg>
							<span class="flex-1">Security lighting</span>
						</li>
						<li class="flex">
							<svg
								class="mr-3 mt-1 h-5 w-5 flex-shrink-0 text-green-500"
								xmlns="http://www.w3.org/2000/svg"
								fill="none"
								viewBox="0 0 24 24"
								stroke-width="2"
								stroke="currentColor"
								aria-hidden="true"
							>
								<path stroke-linecap="round" stroke-linejoin="round" d="m4.5 12.75 6 6 9-13.5" />
							</svg>
							<span class="flex-1">Outdoor power solutions</span>
						</li>
					</ul>
					<div class="mt-auto text-center">
						<a
							href="/agricultural"
							class="font-medium text-green-600 no-underline hover:text-green-700">Learn More →</a
						>
					</div>
				</div>

				<!-- Commercial & Industrial -->
				<div
					class="card flex h-full flex-col rounded-2xl border border-gray-100 bg-white p-8 shadow-xl"
				>
					<div
						class="mx-auto mb-6 flex h-16 w-16 items-center justify-center rounded-xl bg-purple-100"
					>
						<img src="/purple-shop.png" alt="Purple shop" class="h-8 w-8 object-contain" />
					</div>
					<h3 class="mb-5 text-center text-xl font-semibold text-gray-800">
						Commercial & Industrial
					</h3>
					<ul class="mb-6 space-y-3 text-sm text-gray-600">
						<li class="flex">
							<svg
								class="mr-3 mt-1 h-5 w-5 flex-shrink-0 text-green-500"
								xmlns="http://www.w3.org/2000/svg"
								fill="none"
								viewBox="0 0 24 24"
								stroke-width="2"
								stroke="currentColor"
								aria-hidden="true"
							>
								<path stroke-linecap="round" stroke-linejoin="round" d="m4.5 12.75 6 6 9-13.5" />
							</svg>
							<span class="flex-1">Power distribution systems</span>
						</li>
						<li class="flex">
							<svg
								class="mr-3 mt-1 h-5 w-5 flex-shrink-0 text-green-500"
								xmlns="http://www.w3.org/2000/svg"
								fill="none"
								viewBox="0 0 24 24"
								stroke-width="2"
								stroke="currentColor"
								aria-hidden="true"
							>
								<path stroke-linecap="round" stroke-linejoin="round" d="m4.5 12.75 6 6 9-13.5" />
							</svg>
							<span class="flex-1">Machinery connections</span>
						</li>
						<li class="flex">
							<svg
								class="mr-3 mt-1 h-5 w-5 flex-shrink-0 text-green-500"
								xmlns="http://www.w3.org/2000/svg"
								fill="none"
								viewBox="0 0 24 24"
								stroke-width="2"
								stroke="currentColor"
								aria-hidden="true"
							>
								<path stroke-linecap="round" stroke-linejoin="round" d="m4.5 12.75 6 6 9-13.5" />
							</svg>
							<span class="flex-1">Portable Appliance Testing for office & retail spaces</span>
						</li>
						<li class="flex">
							<svg
								class="mr-3 mt-1 h-5 w-5 flex-shrink-0 text-green-500"
								xmlns="http://www.w3.org/2000/svg"
								fill="none"
								viewBox="0 0 24 24"
								stroke-width="2"
								stroke="currentColor"
								aria-hidden="true"
							>
								<path stroke-linecap="round" stroke-linejoin="round" d="m4.5 12.75 6 6 9-13.5" />
							</svg>
							<span class="flex-1">Controls & monitoring</span>
						</li>
					</ul>
					<div class="mt-auto text-center">
						<a
							href="/commercial-industrial"
							class="font-medium text-purple-600 no-underline hover:text-purple-700">Learn More →</a
						>
					</div>
				</div>

				<!-- Sustainability & Energy Savings -->
				<div
					class="card flex h-full flex-col rounded-2xl border border-gray-100 bg-white p-8 shadow-xl"
				>
					<div
						class="mx-auto mb-6 flex h-16 w-16 items-center justify-center rounded-xl bg-yellow-100"
					>
						<img src="/yellow-world.png" alt="Yellow world" class="h-8 w-8 object-contain" />
					</div>
					<h3 class="mb-5 text-center text-xl font-semibold text-gray-800">
						Sustainability, resiliency &amp; efficiency
					</h3>
					<ul class="mb-6 space-y-3 text-sm text-gray-600">
						<li class="flex">
							<svg
								class="mr-3 mt-1 h-5 w-5 flex-shrink-0 text-green-500"
								xmlns="http://www.w3.org/2000/svg"
								fill="none"
								viewBox="0 0 24 24"
								stroke-width="2"
								stroke="currentColor"
								aria-hidden="true"
							>
								<path stroke-linecap="round" stroke-linejoin="round" d="m4.5 12.75 6 6 9-13.5" />
							</svg>
							<span class="flex-1"
								>Energy efficiency &amp; resiliency improvements (e.g. back-up generator
								connections)</span
							>
						</li>
						<li class="flex">
							<svg
								class="mr-3 mt-1 h-5 w-5 flex-shrink-0 text-green-500"
								xmlns="http://www.w3.org/2000/svg"
								fill="none"
								viewBox="0 0 24 24"
								stroke-width="2"
								stroke="currentColor"
								aria-hidden="true"
							>
								<path stroke-linecap="round" stroke-linejoin="round" d="m4.5 12.75 6 6 9-13.5" />
							</svg>
							<span class="flex-1">LED lighting retrofits</span>
						</li>
						<li class="flex">
							<svg
								class="mr-3 mt-1 h-5 w-5 flex-shrink-0 text-green-500"
								xmlns="http://www.w3.org/2000/svg"
								fill="none"
								viewBox="0 0 24 24"
								stroke-width="2"
								stroke="currentColor"
								aria-hidden="true"
							>
								<path stroke-linecap="round" stroke-linejoin="round" d="m4.5 12.75 6 6 9-13.5" />
							</svg>
							<span class="flex-1">Smart home connectivity</span>
						</li>
						<li class="flex">
							<svg
								class="mr-3 mt-1 h-5 w-5 flex-shrink-0 text-green-500"
								xmlns="http://www.w3.org/2000/svg"
								fill="none"
								viewBox="0 0 24 24"
								stroke-width="2"
								stroke="currentColor"
								aria-hidden="true"
							>
								<path stroke-linecap="round" stroke-linejoin="round" d="m4.5 12.75 6 6 9-13.5" />
							</svg>
							<span class="flex-1">Cabling upgrades for power and telecoms</span>
						</li>
					</ul>
					<div class="mt-auto text-center">
						<a
							href="/sustainability-resiliency-efficiency"
							class="font-medium text-yellow-600 no-underline hover:text-yellow-700">Learn More →</a
						>
					</div>
				</div>
			</div>
		</div>
	</div>
</section>

<Footer />
