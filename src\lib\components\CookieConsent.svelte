<script lang="ts">
  import { onMount } from 'svelte';
  import { browser } from '$app/environment';
  import { writable } from 'svelte/store';

  const COOKIE_NAME = 'cookie_consent';
  const COOKIE_MAX_AGE = 60 * 60 * 24 * 365; // 1 year in seconds

  const visible = writable(false);

  onMount(() => {
    if (!browser) return;

    const hasConsent = document.cookie
      .split('; ')
      .find(row => row.startsWith(`${COOKIE_NAME}=`));

    visible.set(!hasConsent);
  });

  function acceptCookies() {
    if (!browser) return;

    document.cookie = `${COOKIE_NAME}=1; max-age=${COOKIE_MAX_AGE}; path=/`;
    visible.set(false);
  }
</script>

{#if $visible}
  <div class="fixed bottom-0 inset-x-0 bg-gray-800 text-white text-sm p-4 z-50 shadow-lg flex flex-col md:flex-row items-center justify-between gap-4">
    <div class="flex-1 text-center md:text-left">
      This site uses essential cookies only. 
      <a href="/privacy-policy" class="underline ml-1 text-blue-300 hover:text-blue-400">See our privacy policy</a> for details.
    </div>
    <button on:click={acceptCookies} class="btn btn-sm text-base-content btn-base-100">
      Dismiss
    </button>
  </div>
{/if}
