<script lang="ts">
	import Footer from '../Footer.svelte';
	import {
		COMPANY_NAME,
		EMAIL,
		COMPANY_DESCRIPTION,
		SEO_TITLE,
		SEO_DESCRIPTION,
		COMPANY_LOCATION,
		PHONE_NUMBER,
		DEBUG_MODE
	} from '$lib/constants.js';

	import { superForm } from 'sveltekit-superforms/client';
	import type { PageData } from './$types';

	export let data: PageData;

	let serverErrors: string[] = [];
	
	const { form, errors, constraints, message, enhance, delayed } = superForm(data.form, {
		resetForm: false, // Don't reset form on failure to preserve error messages
		onResult: ({ result }) => {
			// Handle errors - extract server errors from the result
			if (result.type === 'failure') {
				if (result.data && result.data._errors) {
					serverErrors = result.data._errors;
				}
			} else {
				// Clear server errors on success
				serverErrors = [];
			}
		}
	});
</script>

<svelte:head>
	<title>Contact Us | {SEO_TITLE}</title>
	<meta
		name="description"
		content="Contact our local, family owned and run electrical firm. {SEO_DESCRIPTION}"
	/>
</svelte:head>

<!-- <PERSON>er -->
<section>
	<div class="w-full bg-white shadow-lg">
		<div class="px-4 sm:px-6 md:px-8">
			<!-- Header content -->
			<div class="flex items-start gap-3 py-4">
				<div class="h-14 w-14 flex-shrink-0 overflow-hidden rounded-xl sm:h-12 sm:w-12">
					<img
						src="../logo-icon.png"
						alt="Logo of {COMPANY_NAME}"
						class="h-full w-full object-cover"
					/>
				</div>
				<!-- Company info -->
				<div>
					<a href="/" class="block text-left text-xl font-bold text-gray-800 hover:no-underline">
						{COMPANY_NAME}
					</a>
					<h2 class="text-left text-xs text-neutral/70">{COMPANY_DESCRIPTION}</h2>
				</div>
			</div>
			<!-- Breadcrumb trail -->
			<nav class="breadcrumbs pb-2 text-sm text-gray-600">
				<ul>
					<li>
						<a href="/" class="inline-flex items-center gap-1 hover:text-blue-800">
							<svg
								xmlns="http://www.w3.org/2000/svg"
								viewBox="0 0 16 16"
								fill="currentColor"
								class="size-4"
								aria-hidden="true"
							>
								<path
									d="M8.543 2.232a.75.75 0 0 0-1.085 0l-5.25 5.5A.75.75 0 0 0 2.75 9H4v4a1 1 0 0 0 1 1h1a1 1 0 0 0 1-1v-1a1 1 0 1 1 2 0v1a1 1 0 0 0 1 1h1a1 1 0 0 0 1-1V9h1.25a.75.75 0 0 0 .543-1.268l-5.25-5.5Z"
								/>
							</svg>
							Home
						</a>
					</li>
					<li>
						<span class="text-gray-500">Contact Us</span>
					</li>
				</ul>
			</nav>
		</div>
	</div>
</section>

<!-- Main Content Section -->
<section class="bg-gradient-to-br from-gray-50 to-white py-16">
	<div class="container mx-auto px-4">
		<div class="mx-auto max-w-4xl">
			<h1 class="mb-6 text-left text-4xl font-bold text-gray-800">Contact us</h1>

			<h2 class="mb-8 text-2xl font-semibold text-gray-700">
				As a family-owned and operated business based in {COMPANY_LOCATION}, we serve homes, farms,
				and businesses across our local community. When you choose us, you're choosing a
				locally-based electrical services partner committed to keeping our communities powered
				safely and efficiently.
			</h2>

			<div class="prose prose-lg max-w-none space-y-6 text-gray-600">
				<p>Get in touch to discuss your project:</p>
				<ul class="space-y-3">
					<li class="flex items-center gap-3">
						<svg
							xmlns="http://www.w3.org/2000/svg"
							fill="none"
							viewBox="0 0 24 24"
							stroke-width="1.5"
							stroke="currentColor"
							class="h-5 w-5 text-secondary"
							aria-hidden="true"
							><path
								stroke-linecap="round"
								stroke-linejoin="round"
								d="M14.25 9.75v-4.5m0 4.5h4.5m-4.5 0 6-6m-3 18c-8.284 0-15-6.716-15-15V4.5A2.25 2.25 0 0 1 4.5 2.25h1.372c.516 0 .966.351 1.091.852l1.106 4.423c.11.44-.054.902-.417 1.173l-1.293.97a1.062 1.062 0 0 0-.38 1.21 12.035 12.035 0 0 0 7.143 7.143c.441.162.928-.004 1.21-.38l.97-1.293a1.125 1.125 0 0 1 1.173-.417l4.423 1.106c.5.125.852.575.852 1.091V19.5a2.25 2.25 0 0 1-2.25 2.25h-2.25Z"
							></path></svg
						> <a class="link-hover link text-sm" href="tel:{PHONE_NUMBER}">{PHONE_NUMBER}</a>
					</li>
					<li class="flex items-center gap-3">
						<svg
							xmlns="http://www.w3.org/2000/svg"
							fill="none"
							viewBox="0 0 24 24"
							stroke-width="1.5"
							stroke="currentColor"
							class="h-5 w-5 text-secondary"
							aria-hidden="true"
							><path
								stroke-linecap="round"
								stroke-linejoin="round"
								d="M21.75 9v.906a2.25 2.25 0 0 1-1.183 1.981l-6.478 3.488M2.25 9v.906a2.25 2.25 0 0 0 1.183 1.981l6.478 3.488m8.839 2.51-4.66-2.51m0 0-1.023-.55a2.25 2.25 0 0 0-2.134 0l-1.022.55m0 0-4.661 2.51m16.5 1.615a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V8.844a2.25 2.25 0 0 1 1.183-1.981l7.5-4.039a2.25 2.25 0 0 1 2.134 0l7.5 4.039a2.25 2.25 0 0 1 1.183 1.98V19.5Z"
							></path></svg
						>
						<a
							class="link-hover link text-sm"
							href="mailto:{EMAIL}?subject=Website Enquiry">{EMAIL}</a
						>
					</li>
				</ul>
				<p>
					Or, you can fill in the enquiry form below.
				</p>
			</div>

			<!-- Success message - shown when form is successfully submitted -->
			{#if $message}
				<div class="mt-12 rounded-lg bg-green-100 p-8 text-center">
					<div class="mb-4 flex justify-center">
						<svg
							xmlns="http://www.w3.org/2000/svg"
							fill="none"
							viewBox="0 0 24 24"
							stroke-width="1.5"
							stroke="currentColor"
							class="h-16 w-16 text-green-500"
							aria-hidden="true"
						>
							<path
								stroke-linecap="round"
								stroke-linejoin="round"
								d="M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
							/>
						</svg>
					</div>
					<h3 class="mb-2 text-2xl font-bold text-green-800">Thank you!</h3>
					<p class="mb-6 text-lg text-green-700">{$message}</p>
				</div>
			{:else}
				<!-- Enquiry form - only shown when no success message -->
				<form
					method="POST"
					use:enhance
					class="mt-12 space-y-6 rounded-lg bg-blue-800 p-6 text-base-100 shadow-lg"
				>
					<h3 class="text-xl font-bold">Enquiry form</h3>
					<p class="mb-4 text-sm">
						We'll send you a confirmation email to the address you provided and get back to you as soon as possible.
					</p>


					<!-- Honeypot field for spam protection -->
					<div class="hidden">
						<label for="homepage">Homepage</label>
						<input type="text" id="homepage" name="homepage" tabindex="-1" autocomplete="off" />
					</div>
					
					<div>
						<label for="name" class="mb-1 block font-semibold">Name <span class="text-red-400">*</span></label>
						<input
							id="name"
							name="name"
							type="text"
							placeholder="Your full name (required)"
							class="input input-bordered w-full text-black"
							aria-invalid={$errors.name ? 'true' : undefined}
							bind:value={$form.name}
							{...$constraints.name}
						/>
						{#if $errors.name}
							<p class="mt-1 text-sm text-red-400">{$errors.name}</p>
						{/if}
					</div>

					<div>
						<label for="email" class="mb-1 block font-semibold">Email address <span class="text-red-400">*</span></label>
						<input
							id="email"
							name="email"
							type="email"
							placeholder="<EMAIL> (required)"
							class="input input-bordered w-full text-black"
							aria-invalid={$errors.email ? 'true' : undefined}
							bind:value={$form.email}
							{...$constraints.email}
						/>
						{#if $errors.email}
							<p class="mt-1 text-sm text-red-400">{$errors.email}</p>
						{/if}
					</div>

					<div>
						<label for="phone" class="mb-1 block font-semibold">Telephone number <span class="text-red-400">*</span></label>
						<input
							id="phone"
							name="phone"
							type="tel"
							placeholder="07476 123456 (required)"
							class="input input-bordered w-full text-black"
							aria-invalid={$errors.phone ? 'true' : undefined}
							bind:value={$form.phone}
							{...$constraints.phone}
						/>
						{#if $errors.phone}
							<p class="mt-1 text-sm text-red-400">{$errors.phone}</p>
						{/if}
					</div>

					<div>
						<label for="message" class="mb-1 block font-semibold">Message <span class="text-red-400">*</span></label>
						<textarea
							id="message"
							name="message"
							rows="5"
							maxlength="1500"
							placeholder="Write your message here (required, max 1500 characters)"
							class="textarea textarea-bordered w-full text-black"
							aria-invalid={$errors.message ? 'true' : undefined}
							bind:value={$form.message}
							{...$constraints.message}
						></textarea>
						{#if $errors.message}
							<p class="mt-1 text-sm text-red-400">{$errors.message}</p>
						{/if}
					</div>

					<!-- Show any general form errors -->
					{#if $errors._errors && $errors._errors.length > 0}
						<div class="rounded-lg bg-red-100 border border-red-300 p-4 text-red-800">
							<div class="flex items-start gap-3">
								<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6 text-red-600 flex-shrink-0 mt-0.5">
									<path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m9-.75a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 3.75h.008v.008H12v-.008Z" />
								</svg>
								<div>
									<h4 class="font-semibold text-red-900">Submission Error</h4>
									<ul class="mt-2 list-disc list-inside space-y-1">
										{#each $errors._errors as error}
											<li>{error}</li>
										{/each}
									</ul>
								</div>
							</div>
						</div>
					{/if}
					
					<!-- Show server errors from onResult callback -->
					{#if serverErrors.length > 0}
						<div class="rounded-lg bg-red-100 border border-red-300 p-4 text-red-800">
							<div class="flex items-start gap-3">
								<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
									<path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z" />
								 </svg>
								 
								<div>
									<h4 class="font-semibold text-red-900">Submission Error</h4>
									<ul class="mt-2 list-disc list-inside space-y-1">
										{#each serverErrors as error}
											<li>{error}</li>
										{/each}
									</ul>
								</div>
							</div>
						</div>
					{/if}
					<button 
						type="submit" 
						class="btn text-base-content {$delayed ? '!bg-base-200' : 'btn-base-100'}"
						disabled={$delayed}
					>
						{#if $delayed}
							<span class="loading loading-spinner loading-sm"></span>
							Sending...
						{:else}
							Submit Your Enquiry
						{/if}
					</button>
				</form>
			{/if}
		</div>
	</div>
</section>

<Footer />
