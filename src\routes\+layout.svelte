<script lang="ts">
	import '../app.css';
	import { afterNavigate } from '$app/navigation';
	import CookieConsent from '$lib/components/CookieConsent.svelte';
	import JobStatusBanner from '$lib/components/JobStatusBanner.svelte';

	let { children } = $props();

	// Track route changes
	afterNavigate((nav) => {
		if (typeof window !== 'undefined' && window.gtag) {
			window.gtag('config', 'G-BTF092ZXYF', {
				page_path: nav.to?.url.pathname
			});
		}
	});
</script>

<div class="app">
	<!-- Job Status Banner -->
	<JobStatusBanner />

	<main>
		{@render children()}
	</main>

	<!-- <PERSON>ie consent banner to be legally compliant -->
	<CookieConsent />
</div>

<style>
	.app {
		display: flex;
		flex-direction: column;
		min-height: 100vh;
	}

	main {
		flex: 1;
		display: flex;
		flex-direction: column;
		padding: 1rem;
		width: 100%;
		max-width: 64rem;
		margin: 0 auto;
		box-sizing: border-box;
	}

	@media (min-width: 480px) {
		main {
			padding: 1rem;
		}
	}
</style>
