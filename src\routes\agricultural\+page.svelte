<script>
	import Footer from '../Footer.svelte';
	import { COMPANY_NAME, COMPANY_DESCRIPTION, SEO_TITLE, SEO_DESCRIPTION } from '$lib/constants.js';
</script>

<svelte:head>
	<title>Farm &amp; Agricultural Site Electrician | {SEO_TITLE}</title>
	<meta
		name="description"
		content="Farm and agricultural site electrician in the Scottish Borders. {SEO_DESCRIPTION}"
	/>
</svelte:head>

<!-- Brand Header -->
<section>
	<div class="w-full bg-white shadow-lg">
		<div class="px-4 sm:px-6 md:px-8">
			<!-- Header content -->
			<div class="flex items-start gap-3 py-4">
				<div class="h-14 w-14 flex-shrink-0 overflow-hidden rounded-xl sm:h-12 sm:w-12">
					<img
						src="../logo-icon.png"
						alt="Logo of {COMPANY_NAME}"
						class="h-full w-full object-cover"
					/>
				</div>
				<!-- Company info -->
				<div>
					<a href="/" class="block text-left text-xl font-bold text-gray-800 hover:no-underline">
						{COMPANY_NAME}
					</a>
					<h2 class="text-left text-xs text-neutral/70">{COMPANY_DESCRIPTION}</h2>
				</div>
			</div>
			<!-- Breadcrumb trail -->
			<nav class="breadcrumbs pb-2 text-sm text-gray-600">
				<ul>
					<li>
						<a href="/" class="inline-flex items-center gap-1 hover:text-blue-800">
							<svg
								xmlns="http://www.w3.org/2000/svg"
								viewBox="0 0 16 16"
								fill="currentColor"
								class="size-4"
								aria-hidden="true"
							>
								<path
									d="M8.543 2.232a.75.75 0 0 0-1.085 0l-5.25 5.5A.75.75 0 0 0 2.75 9H4v4a1 1 0 0 0 1 1h1a1 1 0 0 0 1-1v-1a1 1 0 1 1 2 0v1a1 1 0 0 0 1 1h1a1 1 0 0 0 1-1V9h1.25a.75.75 0 0 0 .543-1.268l-5.25-5.5Z"
								/>
							</svg>
							Home
						</a>
					</li>
					<li>
						<span class="text-gray-500">Agricultural Electrical</span>
					</li>
				</ul>
			</nav>
		</div>
	</div>
</section>

<!-- Main Content Section -->
<section class="bg-gradient-to-br from-gray-50 to-white py-16">
	<div class="container mx-auto px-4">
		<div class="mx-auto max-w-4xl">
			<h1 class="mb-6 text-left text-4xl font-bold text-gray-800">
				Electrical services for your farm &amp; agricultural site
			</h1>

			<h2 class="mb-8 text-2xl font-semibold text-gray-700">
				We offer reliable electrical services for farms, smallholdings, and rural properties across
				the Scottish Borders - delivered by a local company that understands the unique demands of
				agricultural work.
			</h2>

			<div class="prose prose-lg max-w-none space-y-6 text-gray-600">
				<p>
					We support farmers and landowners throughout the Borders - helping keep essential farming
					systems running safely and efficiently. From sheds and workshops to storage units, we
					carry out installations and repairs that are built to last in tough environments.
				</p>
				<p>
					We know that uptime is critical. That’s why we offer reliable fault finding, and tailored
					solutions that match the layout and demands of your land or business.
				</p>
				<h2 class="mt-6 text-xl font-semibold">
					What agricultural electrical services do we typically offer?
				</h2>
				<ul class="list-inside list-disc space-y-2 text-base-content/80">
					<li>Power connections for sheds, barns, and outbuildings</li>
					<li>Farm site security lighting</li>
					<li>Electrical supply to machinery, tools, and farm equipment</li>
					<li>External lighting and security systems for yards and perimeters</li>
				</ul>

				<p>
					From our base in the middle of the Scottish Borders, we’re a family-run business, fully
					insured and always happy to visit your site to discuss the best way to get the job done -
					safely and efficiently.
				</p>
			</div>

			<!-- Call to action -->
			<div class="mt-12 rounded-lg bg-blue-800 p-4 text-left text-base-100 shadow-lg">
				<div class="p-4">
					<h3 class="mb-1 text-xl font-bold">Ready to find out more?</h3>
					<p class="mb-4 text-sm">
						Find out if we cover your area of the Scottish Borders, and contact us today for a chat.
					</p>

					<div class="flex flex-col gap-3 sm:flex-row">
						<a href="/contact-us" class="btn-base-100 btn flex items-center justify-start">
							<svg
								xmlns="http://www.w3.org/2000/svg"
								fill="none"
								viewBox="0 0 24 24"
								stroke-width="1.5"
								stroke="currentColor"
								class="size-6"
								aria-hidden="true"
							>
								<path
									stroke-linecap="round"
									stroke-linejoin="round"
									d="M15.59 14.37a6 6 0 0 1-5.84 7.38v-4.8m5.84-2.58a14.98 14.98 0 0 0 6.16-12.12A14.98 14.98 0 0 0 9.631 8.41m5.96 5.96a14.926 14.926 0 0 1-5.841 2.58m-.119-8.54a6 6 0 0 0-7.381 5.84h4.8m2.581-5.84a14.927 14.927 0 0 0-2.58 5.84m2.699 2.7c-.103.021-.207.041-.311.06a15.09 15.09 0 0 1-2.448-2.448 14.9 14.9 0 0 1 .06-.312m-2.24 2.39a4.493 4.493 0 0 0-1.757 4.306 4.493 4.493 0 0 0 4.306-1.758M16.5 9a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0Z"
								/>
							</svg>
							Contact Us
						</a>
						<a
							href="/areas-we-cover"
							class="btn-base-100 btn btn-outline flex items-center justify-start text-base-100"
						>
							<svg
								xmlns="http://www.w3.org/2000/svg"
								fill="none"
								viewBox="0 0 24 24"
								stroke-width="1.5"
								stroke="currentColor"
								class="size-6"
								aria-hidden="true"
							>
								<path
									stroke-linecap="round"
									stroke-linejoin="round"
									d="M9 6.75V15m6-6v8.25m.503 3.498 4.875-2.437c.381-.19.622-.58.622-1.006V4.82c0-.836-.88-1.38-1.628-1.006l-3.869 1.934c-.317.159-.69.159-1.006 0L9.503 3.252a1.125 1.125 0 0 0-1.006 0L3.622 5.689C3.24 5.88 3 6.27 3 6.695V19.18c0 .836.88 1.38 1.628 1.006l3.869-1.934c.317-.159.69-.159 1.006 0l4.994 2.497c.317.158.69.158 1.006 0Z"
								/>
							</svg>

							Check Our Coverage Areas
						</a>
					</div>
				</div>
			</div>
		</div>
	</div>
</section>

<Footer />
